#!/usr/bin/env ts-node

/**
 * Comprehensive test script for the receipting system
 * Tests all components including validation, parsing, and receipt creation
 */

import { parseTransactionMessage } from '../src/utils/transactionParser';
import { 
  validateTransactionId, 
  validatePhone<PERSON><PERSON>ber, 
  validateAmount, 
  validateCustomerName,
  validateQuantity,
  validateServiceId,
  validateTransactionDate,
  validateReceiptData,
  validateReceiptItem,
  validateTransactionData
} from '../src/utils/validation';

// Test data
const testMessages = [
  "TEE5PIQ2H7 completed. You have received KES 1500 from Wesley Moturi ************ for account MOCKY GRAPHICS LIMITED 7934479 on 14/05/2025 at 05:00 PM. KCB Go Ahead.",
  "TED3JYV8SX completed. You have received KES 2500 from DAMARIS JUMA ************ for account MOCKY GRAPHICS LIMITED 7934479 on 13/05/2025 at 12:55 PM. KCB Go Ahead.",
  "TDF9TBL1BV Confirmed. Ksh2,500.00 received from JOHN DOE ************ on 15/05/2025 at 10:30 AM. New M-PESA balance is Ksh5,000.00",
  "Invalid message without proper format",
  ""
];

const testReceiptData = {
  transactionId: 'TEE5PIQ2H7',
  customerName: 'John Doe',
  phoneNumber: '************',
  email: '<EMAIL>',
  notes: 'Test receipt',
  items: [
    {
      serviceId: '123e4567-e89b-12d3-a456-************',
      quantity: 2,
      unitPrice: 1000,
      description: 'Logo design'
    }
  ]
};

// Test counters
let totalTests = 0;
let passedTests = 0;
let failedTests = 0;

function runTest(testName: string, testFunction: () => void): void {
  totalTests++;
  try {
    testFunction();
    console.log(`✅ ${testName}`);
    passedTests++;
  } catch (error) {
    console.log(`❌ ${testName}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    failedTests++;
  }
}

function assertEqual(actual: any, expected: any, message?: string): void {
  if (actual !== expected) {
    throw new Error(`${message || 'Assertion failed'}: expected ${expected}, got ${actual}`);
  }
}

function assertNotNull(value: any, message?: string): void {
  if (value === null || value === undefined) {
    throw new Error(message || 'Expected non-null value');
  }
}

function assertThrows(fn: () => void, message?: string): void {
  try {
    fn();
    throw new Error(message || 'Expected function to throw');
  } catch (error) {
    // Expected to throw
  }
}

console.log('🧪 Starting Receipting System Tests\n');

// Transaction Parser Tests
console.log('📝 Transaction Parser Tests');

runTest('Parse valid M-Pesa message', () => {
  const result = parseTransactionMessage(testMessages[0]);
  assertNotNull(result);
  assertEqual(result!.transactionId, 'TEE5PIQ2H7');
  assertEqual(result!.amount, 1500);
  assertEqual(result!.customerName, 'Wesley Moturi');
  assertEqual(result!.phoneNumber, '************');
});

runTest('Parse alternative M-Pesa format', () => {
  const result = parseTransactionMessage(testMessages[2]);
  assertNotNull(result);
  assertEqual(result!.transactionId, 'TDF9TBL1BV');
  assertEqual(result!.amount, 2500);
  assertEqual(result!.customerName, 'JOHN DOE');
  assertEqual(result!.phoneNumber, '************');
});

runTest('Handle invalid message', () => {
  const result = parseTransactionMessage(testMessages[3]);
  assertEqual(result, null);
});

runTest('Handle empty message', () => {
  const result = parseTransactionMessage(testMessages[4]);
  assertEqual(result, null);
});

// Validation Tests
console.log('\n🔍 Validation Tests');

runTest('Validate transaction ID', () => {
  assertEqual(validateTransactionId('TEE5PIQ2H7'), 'TEE5PIQ2H7');
  assertThrows(() => validateTransactionId('INVALID'));
  assertThrows(() => validateTransactionId(''));
});

runTest('Validate phone number', () => {
  assertEqual(validatePhoneNumber('************'), '************');
  assertEqual(validatePhoneNumber('0720335304'), '************');
  assertEqual(validatePhoneNumber('720335304'), '************');
  assertThrows(() => validatePhoneNumber('123'));
  assertThrows(() => validatePhoneNumber(''));
});

runTest('Validate amount', () => {
  assertEqual(validateAmount(100), 100);
  assertEqual(validateAmount('1500.50'), 1500.5);
  assertEqual(validateAmount(100.999), 101);
  assertThrows(() => validateAmount(0));
  assertThrows(() => validateAmount(-100));
  assertThrows(() => validateAmount('invalid'));
});

runTest('Validate customer name', () => {
  assertEqual(validateCustomerName('John Doe'), 'John Doe');
  assertEqual(validateCustomerName('Mary-Jane Smith'), 'Mary-Jane Smith');
  assertEqual(validateCustomerName("O'Connor"), "O'Connor");
  assertThrows(() => validateCustomerName(''));
  assertThrows(() => validateCustomerName('A'));
  assertThrows(() => validateCustomerName('John123'));
});

runTest('Validate quantity', () => {
  assertEqual(validateQuantity(1), 1);
  assertEqual(validateQuantity('5'), 5);
  assertEqual(validateQuantity(100), 100);
  assertThrows(() => validateQuantity(0));
  assertThrows(() => validateQuantity(-1));
  assertThrows(() => validateQuantity(1.5));
  assertThrows(() => validateQuantity('invalid'));
});

runTest('Validate service ID', () => {
  const uuid = '123e4567-e89b-12d3-a456-************';
  assertEqual(validateServiceId(uuid), uuid);
  assertEqual(validateServiceId(uuid.toUpperCase()), uuid);
  assertThrows(() => validateServiceId('invalid-uuid'));
  assertThrows(() => validateServiceId(''));
});

// Receipt Data Validation Tests
console.log('\n📋 Receipt Data Validation Tests');

runTest('Validate complete receipt data', () => {
  const result = validateReceiptData(testReceiptData);
  assertEqual(result.transactionId, 'TEE5PIQ2H7');
  assertEqual(result.customerName, 'John Doe');
  assertEqual(result.phoneNumber, '************');
  assertEqual(result.email, '<EMAIL>');
  assertEqual(result.items.length, 1);
});

runTest('Validate receipt data with optional fields', () => {
  const minimalData = {
    transactionId: 'TEE5PIQ2H7',
    items: [
      {
        serviceId: '123e4567-e89b-12d3-a456-************',
        quantity: 1
      }
    ]
  };
  
  const result = validateReceiptData(minimalData);
  assertEqual(result.email, null);
  assertEqual(result.notes, null);
  assertEqual(result.customerName, '');
  assertEqual(result.phoneNumber, '');
});

runTest('Validate receipt data missing required fields', () => {
  assertThrows(() => validateReceiptData({}));
  assertThrows(() => validateReceiptData({ transactionId: 'TEE5PIQ2H7' }));
});

runTest('Validate receipt item', () => {
  const item = {
    serviceId: '123e4567-e89b-12d3-a456-************',
    quantity: 2,
    unitPrice: 1000,
    description: 'Logo design'
  };
  
  const result = validateReceiptItem(item);
  assertEqual(result.serviceId, '123e4567-e89b-12d3-a456-************');
  assertEqual(result.quantity, 2);
  assertEqual(result.unitPrice, 1000);
  assertEqual(result.description, 'Logo design');
});

runTest('Validate receipt item with optional fields', () => {
  const item = {
    serviceId: '123e4567-e89b-12d3-a456-************',
    quantity: 1
  };
  
  const result = validateReceiptItem(item);
  assertEqual(result.unitPrice, 0);
  assertEqual(result.description, null);
});

// Transaction Data Validation Tests
console.log('\n💳 Transaction Data Validation Tests');

runTest('Validate transaction data', () => {
  const data = {
    transactionId: 'TEE5PIQ2H7',
    amount: 1500,
    customerName: 'John Doe',
    phoneNumber: '************',
    transactionDate: new Date(),
    rawMessage: 'Test message'
  };
  
  const result = validateTransactionData(data);
  assertEqual(result.transactionId, 'TEE5PIQ2H7');
  assertEqual(result.amount, 1500);
  assertEqual(result.customerName, 'John Doe');
  assertEqual(result.phoneNumber, '************');
});

// Performance Tests
console.log('\n⚡ Performance Tests');

runTest('Parse multiple messages performance', () => {
  const start = Date.now();
  const validMessages = testMessages.slice(0, 3); // Only valid messages
  
  for (let i = 0; i < 100; i++) {
    for (const message of validMessages) {
      parseTransactionMessage(message);
    }
  }
  
  const duration = Date.now() - start;
  console.log(`   Parsed 300 messages in ${duration}ms (${(duration/300).toFixed(2)}ms per message)`);
  
  if (duration > 5000) { // 5 seconds for 300 messages
    throw new Error(`Performance test failed: took ${duration}ms`);
  }
});

// Summary
console.log('\n📊 Test Summary');
console.log(`Total Tests: ${totalTests}`);
console.log(`Passed: ${passedTests} ✅`);
console.log(`Failed: ${failedTests} ❌`);
console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

if (failedTests > 0) {
  console.log('\n❌ Some tests failed. Please review the issues above.');
  process.exit(1);
} else {
  console.log('\n🎉 All tests passed! The receipting system is working correctly.');
  process.exit(0);
}
