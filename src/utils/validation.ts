import { z } from 'zod';
import DOMPurify from 'isomorphic-dompurify';

// Validation error class
export class TeamMemberValidationError extends Error {
  constructor(
    message: string,
    public field?: string,
    public code?: string
  ) {
    super(message);
    this.name = 'TeamMemberValidationError';
  }
}

/**
 * Sanitizes HTML content to prevent XSS attacks
 */
export function sanitizeHtml(input: string): string {
  return DOMPurify.sanitize(input, {
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: []
  });
}

/**
 * Sanitizes and validates text input
 */
export function sanitizeText(input: string, maxLength: number = 1000): string {
  if (typeof input !== 'string') {
    throw new TeamMemberValidationError('Input must be a string');
  }

  // Remove HTML tags and sanitize
  const sanitized = sanitizeHtml(input.trim());

  // Check length
  if (sanitized.length > maxLength) {
    throw new TeamMemberValidationError(`Input exceeds maximum length of ${maxLength} characters`);
  }

  return sanitized;
}

/**
 * Validates and sanitizes URL input
 */
export function sanitizeUrl(url: string): string | null {
  if (!url || url.trim() === '') {
    return null;
  }

  const sanitized = sanitizeText(url, 255);

  try {
    const urlObj = new URL(sanitized);
    // Only allow https URLs
    if (urlObj.protocol !== 'https:') {
      throw new TeamMemberValidationError('Only HTTPS URLs are allowed');
    }
    return urlObj.toString();
  } catch (error) {
    throw new TeamMemberValidationError('Invalid URL format');
  }
}

/**
 * Validates email format
 */
export function validateEmail(email: string): string | null {
  if (!email || email.trim() === '') {
    return null;
  }

  const sanitized = sanitizeText(email, 100);
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  if (!emailRegex.test(sanitized)) {
    throw new TeamMemberValidationError('Invalid email format');
  }

  return sanitized.toLowerCase();
}

/**
 * Validates image file
 */
export function validateImageFile(file: File): void {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  const maxSize = 15 * 1024 * 1024; // 15MB

  if (!allowedTypes.includes(file.type)) {
    throw new TeamMemberValidationError(
      'Invalid image format. Only JPEG, PNG, GIF, and WebP are allowed',
      'image',
      'INVALID_FORMAT'
    );
  }

  if (file.size > maxSize) {
    throw new TeamMemberValidationError(
      `Image file is too large (${Math.round(file.size / 1024 / 1024)}MB). Maximum size is 15MB`,
      'image',
      'FILE_TOO_LARGE'
    );
  }

  if (file.size === 0) {
    throw new TeamMemberValidationError(
      'Image file is empty',
      'image',
      'EMPTY_FILE'
    );
  }
}

/**
 * Validates order number
 */
export function validateOrder(order: any): number {
  const parsed = parseInt(order, 10);

  if (isNaN(parsed)) {
    throw new TeamMemberValidationError('Order must be a valid number');
  }

  if (parsed < 0) {
    throw new TeamMemberValidationError('Order must be non-negative');
  }

  if (parsed > 999) {
    throw new TeamMemberValidationError('Order must be less than 1000');
  }

  return parsed;
}

/**
 * Receipt validation utilities
 */
export interface ValidatedReceiptData {
  transactionId: string;
  customerName: string;
  phoneNumber: string;
  email: string | null;
  notes: string | null;
  items: ValidatedReceiptItem[];
}

export interface ValidatedReceiptItem {
  serviceId: string;
  quantity: number;
  unitPrice: number;
  description: string | null;
}

export interface ValidatedTransactionData {
  transactionId: string;
  amount: number;
  customerName: string;
  phoneNumber: string;
  transactionDate: Date;
  rawMessage: string;
}

/**
 * Validates M-Pesa transaction ID format
 */
export function validateTransactionId(transactionId: string): string {
  if (!transactionId || typeof transactionId !== 'string') {
    throw new TeamMemberValidationError('Transaction ID is required');
  }

  const sanitized = sanitizeText(transactionId, 20);

  // M-Pesa transaction ID format: 2-3 letters, 1 digit, 6-7 alphanumeric characters
  if (!sanitized.match(/^[A-Z]{2,3}\d[A-Z0-9]{6,7}$/)) {
    throw new TeamMemberValidationError('Invalid M-Pesa transaction ID format');
  }

  return sanitized;
}

/**
 * Validates phone number format (Kenyan format)
 */
export function validatePhoneNumber(phoneNumber: string): string {
  if (!phoneNumber || typeof phoneNumber !== 'string') {
    throw new TeamMemberValidationError('Phone number is required');
  }

  const sanitized = sanitizeText(phoneNumber, 15);

  // Remove any non-digit characters
  const digitsOnly = sanitized.replace(/\D/g, '');

  // Check for valid Kenyan phone number formats
  if (digitsOnly.match(/^254\d{9}$/)) {
    return digitsOnly;
  } else if (digitsOnly.match(/^0\d{9}$/)) {
    return '254' + digitsOnly.substring(1);
  } else if (digitsOnly.match(/^[17]\d{8}$/)) {
    return '254' + digitsOnly;
  }

  throw new TeamMemberValidationError('Invalid phone number format. Must be a valid Kenyan phone number');
}

/**
 * Validates monetary amount
 */
export function validateAmount(amount: any): number {
  if (amount === null || amount === undefined) {
    throw new TeamMemberValidationError('Amount is required');
  }

  const parsed = typeof amount === 'string' ? parseFloat(amount) : Number(amount);

  if (isNaN(parsed)) {
    throw new TeamMemberValidationError('Amount must be a valid number');
  }

  if (parsed <= 0) {
    throw new TeamMemberValidationError('Amount must be greater than zero');
  }

  if (parsed > 10000000) { // 10 million limit
    throw new TeamMemberValidationError('Amount exceeds maximum limit');
  }

  // Round to 2 decimal places
  return Math.round(parsed * 100) / 100;
}

/**
 * Validates customer name
 */
export function validateCustomerName(name: string): string {
  if (!name || typeof name !== 'string') {
    throw new TeamMemberValidationError('Customer name is required');
  }

  const sanitized = sanitizeText(name, 100);

  if (sanitized.length < 2) {
    throw new TeamMemberValidationError('Customer name must be at least 2 characters long');
  }

  // Allow letters, spaces, hyphens, and apostrophes
  if (!sanitized.match(/^[a-zA-Z\s\-'\.]+$/)) {
    throw new TeamMemberValidationError('Customer name contains invalid characters');
  }

  return sanitized.trim();
}

/**
 * Validates receipt item quantity
 */
export function validateQuantity(quantity: any): number {
  if (quantity === null || quantity === undefined) {
    throw new TeamMemberValidationError('Quantity is required');
  }

  const parsed = typeof quantity === 'string' ? parseInt(quantity, 10) : Number(quantity);

  if (isNaN(parsed) || !Number.isInteger(parsed)) {
    throw new TeamMemberValidationError('Quantity must be a valid integer');
  }

  if (parsed <= 0) {
    throw new TeamMemberValidationError('Quantity must be greater than zero');
  }

  if (parsed > 1000) {
    throw new TeamMemberValidationError('Quantity cannot exceed 1000');
  }

  return parsed;
}

/**
 * Validates service ID (UUID format)
 */
export function validateServiceId(serviceId: string): string {
  if (!serviceId || typeof serviceId !== 'string') {
    throw new TeamMemberValidationError('Service ID is required');
  }

  const sanitized = sanitizeText(serviceId, 50);

  // UUID format validation
  if (!sanitized.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
    throw new TeamMemberValidationError('Invalid service ID format');
  }

  return sanitized.toLowerCase();
}

/**
 * Validates transaction date
 */
export function validateTransactionDate(date: any): Date {
  if (!date) {
    throw new TeamMemberValidationError('Transaction date is required');
  }

  const parsed = new Date(date);

  if (isNaN(parsed.getTime())) {
    throw new TeamMemberValidationError('Invalid transaction date');
  }

  const now = new Date();
  const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
  const oneDayFromNow = new Date(now.getTime() + 24 * 60 * 60 * 1000);

  if (parsed < oneYearAgo) {
    throw new TeamMemberValidationError('Transaction date cannot be more than one year ago');
  }

  if (parsed > oneDayFromNow) {
    throw new TeamMemberValidationError('Transaction date cannot be in the future');
  }

  return parsed;
}

/**
 * Validates complete receipt data
 */
export function validateReceiptData(data: any): ValidatedReceiptData {
  const errors: string[] = [];

  try {
    // Validate transaction ID
    const transactionId = validateTransactionId(data.transactionId);

    // Validate customer name (optional override)
    const customerName = data.customerName ? validateCustomerName(data.customerName) : '';

    // Validate phone number (optional override)
    const phoneNumber = data.phoneNumber ? validatePhoneNumber(data.phoneNumber) : '';

    // Validate email (optional)
    const email = data.email ? validateEmail(data.email) : null;

    // Validate notes (optional)
    const notes = data.notes ? sanitizeText(data.notes, 500) : null;

    // Validate items array
    if (!data.items || !Array.isArray(data.items) || data.items.length === 0) {
      errors.push('At least one receipt item is required');
    }

    const items: ValidatedReceiptItem[] = [];
    if (data.items && Array.isArray(data.items)) {
      for (let i = 0; i < data.items.length; i++) {
        try {
          const item = validateReceiptItem(data.items[i]);
          items.push(item);
        } catch (error) {
          errors.push(`Item ${i + 1}: ${error instanceof Error ? error.message : 'Invalid item'}`);
        }
      }
    }

    if (errors.length > 0) {
      throw new TeamMemberValidationError(errors.join(', '));
    }

    return {
      transactionId,
      customerName,
      phoneNumber,
      email,
      notes,
      items
    };
  } catch (error) {
    if (error instanceof TeamMemberValidationError) {
      throw error;
    }
    throw new TeamMemberValidationError('Receipt validation failed: ' + (error as Error).message);
  }
}

/**
 * Validates individual receipt item
 */
export function validateReceiptItem(data: any): ValidatedReceiptItem {
  try {
    const serviceId = validateServiceId(data.serviceId);
    const quantity = validateQuantity(data.quantity);

    // Unit price is optional - will be fetched from service if not provided
    let unitPrice = 0;
    if (data.unitPrice !== undefined && data.unitPrice !== null) {
      unitPrice = validateAmount(data.unitPrice);
    }

    // Description is optional
    const description = data.description ? sanitizeText(data.description, 200) : null;

    return {
      serviceId,
      quantity,
      unitPrice,
      description
    };
  } catch (error) {
    if (error instanceof TeamMemberValidationError) {
      throw error;
    }
    throw new TeamMemberValidationError('Receipt item validation failed: ' + (error as Error).message);
  }
}

/**
 * Validates transaction data from parsed message
 */
export function validateTransactionData(data: any): ValidatedTransactionData {
  try {
    const transactionId = validateTransactionId(data.transactionId);
    const amount = validateAmount(data.amount);
    const customerName = validateCustomerName(data.customerName);
    const phoneNumber = validatePhoneNumber(data.phoneNumber);
    const transactionDate = validateTransactionDate(data.transactionDate);

    // Raw message should be preserved as-is but sanitized
    const rawMessage = data.rawMessage ? sanitizeText(data.rawMessage, 1000) : '';

    return {
      transactionId,
      amount,
      customerName,
      phoneNumber,
      transactionDate,
      rawMessage
    };
  } catch (error) {
    if (error instanceof TeamMemberValidationError) {
      throw error;
    }
    throw new TeamMemberValidationError('Transaction validation failed: ' + (error as Error).message);
  }
}

/**
 * Comprehensive validation for team member data
 */
export interface ValidatedTeamMemberData {
  name: string;
  role: string;
  bio: string;
  order: number;
  linkedinUrl: string | null;
  twitterUrl: string | null;
  githubUrl: string | null;
  emailAddress: string | null;
}

export function validateTeamMemberData(data: any): ValidatedTeamMemberData {
  const errors: string[] = [];

  try {
    // Validate and sanitize name
    const name = sanitizeText(data.name, 100);
    if (!name) {
      errors.push('Name is required');
    } else if (!/^[a-zA-Z\s\-'\.]+$/.test(name)) {
      errors.push('Name contains invalid characters');
    }

    // Validate and sanitize role
    const role = sanitizeText(data.role, 100);
    if (!role) {
      errors.push('Role is required');
    } else if (!/^[a-zA-Z\s\-|&,\.]+$/.test(role)) {
      errors.push('Role contains invalid characters');
    }

    // Validate and sanitize bio
    const bio = sanitizeText(data.bio, 1000);
    if (!bio) {
      errors.push('Bio is required');
    } else if (bio.length < 10) {
      errors.push('Bio must be at least 10 characters');
    }

    // Validate order
    const order = validateOrder(data.order);

    // Validate optional URLs
    const linkedinUrl = data.linkedinUrl ? sanitizeUrl(data.linkedinUrl) : null;
    const twitterUrl = data.twitterUrl ? sanitizeUrl(data.twitterUrl) : null;
    const githubUrl = data.githubUrl ? sanitizeUrl(data.githubUrl) : null;

    // Validate LinkedIn URL format
    if (linkedinUrl && !/^https:\/\/(www\.)?linkedin\.com\/in\/[a-zA-Z0-9\-]+\/?$/.test(linkedinUrl)) {
      errors.push('Invalid LinkedIn profile URL format');
    }

    // Validate Twitter URL format
    if (twitterUrl && !/^https:\/\/(www\.)?(twitter\.com|x\.com)\/[a-zA-Z0-9_]+\/?$/.test(twitterUrl)) {
      errors.push('Invalid Twitter profile URL format');
    }

    // Validate GitHub URL format
    if (githubUrl && !/^https:\/\/(www\.)?github\.com\/[a-zA-Z0-9\-]+\/?$/.test(githubUrl)) {
      errors.push('Invalid GitHub profile URL format');
    }

    // Validate email
    const emailAddress = data.emailAddress ? validateEmail(data.emailAddress) : null;

    if (errors.length > 0) {
      throw new TeamMemberValidationError(errors.join(', '));
    }

    return {
      name,
      role,
      bio,
      order,
      linkedinUrl,
      twitterUrl,
      githubUrl,
      emailAddress
    };
  } catch (error) {
    if (error instanceof TeamMemberValidationError) {
      throw error;
    }
    throw new TeamMemberValidationError('Validation failed: ' + (error as Error).message);
  }
}

/**
 * Rate limiting utility
 */
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function checkRateLimit(
  identifier: string,
  maxRequests: number = 10,
  windowMs: number = 60000
): boolean {
  const now = Date.now();
  const record = rateLimitMap.get(identifier);

  if (!record || now > record.resetTime) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (record.count >= maxRequests) {
    return false;
  }

  record.count++;
  return true;
}

/**
 * Clean up expired rate limit entries
 */
export function cleanupRateLimit(): void {
  const now = Date.now();
  for (const [key, record] of rateLimitMap.entries()) {
    if (now > record.resetTime) {
      rateLimitMap.delete(key);
    }
  }
}

// Clean up every 5 minutes
setInterval(cleanupRateLimit, 5 * 60 * 1000);
