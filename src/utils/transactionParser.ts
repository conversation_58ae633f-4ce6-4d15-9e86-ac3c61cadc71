/**
 * Utility for parsing M-PESA transaction messages
 * Enhanced with comprehensive validation and error handling
 */

import {
  validateTransactionId,
  validateAmount,
  validateCustomerName,
  validatePhoneNumber,
  validateTransactionDate,
  TeamMemberValidationError
} from './validation';

interface TransactionData {
  transactionId: string;
  amount: number;
  customerName: string;
  phoneNumber: string;
  transactionDate: Date;
  rawMessage: string;
}

// Enhanced regex patterns for better parsing
const TRANSACTION_ID_PATTERNS = [
  /([A-Z]{2,3}\d[A-Z0-9]{6,7})/,  // Standard M-Pesa format
  /MPESA\s+confirmation\.?\s+([A-Z]{2,3}\d[A-Z0-9]{6,7})/i,  // With MPESA prefix
  /([A-Z]{2,3}\d[A-Z0-9]{6,7})\s+confirmed/i  // With confirmed suffix
];

const AMOUNT_PATTERNS = [
  /KES\s+([0-9,]+(?:\.[0-9]{2})?)/i,
  /Ksh\.?\s*([0-9,]+(?:\.[0-9]{2})?)/i,
  /([0-9,]+(?:\.[0-9]{2})?)\s+shillings/i,
  /received\s+([0-9,]+(?:\.[0-9]{2})?)/i
];

const CUSTOMER_NAME_PATTERNS = [
  /from\s+([A-Z][A-Z\s]+?)\s+\d/i,
  /from\s+([A-Z][A-Z\s]+?)(?:\s+for|\s+on|\s*$)/i,
  /received\s+from\s+([A-Z][A-Z\s]+)/i
];

const PHONE_NUMBER_PATTERNS = [
  /(\d{12})/,  // 12 digits with country code
  /(\d{10})/,  // 10 digits without country code
  /(\d{3}[\s-]\d{3}[\s-]\d{3}[\s-]\d{3})/,  // With separators
  /254(\d{9})/,  // Explicit Kenya country code
  /\+254\s*(\d{9})/  // With plus sign
];

const DATE_PATTERNS = [
  /on\s+(\d{1,2}\/\d{1,2}\/\d{4})\s+at\s+(\d{1,2}:\d{2}\s*(?:AM|PM))/i,
  /(\d{1,2}\/\d{1,2}\/\d{4})\s+at\s+(\d{1,2}:\d{2})/i,
  /on\s+(\d{1,2}-\d{1,2}-\d{4})/i
];

/**
 * Parse an M-PESA transaction message to extract relevant information
 * Enhanced with comprehensive validation and error handling
 * @param message The raw M-PESA transaction message
 * @returns Extracted transaction data or null if parsing fails
 */
export function parseTransactionMessage(message: string): TransactionData | null {
  const startTime = Date.now();

  try {
    console.log(`[Transaction Parser] Starting to parse message: ${message.substring(0, 100)}...`);

    // Step 1: Basic validation and cleanup
    if (!message || typeof message !== 'string') {
      console.error('[Transaction Parser] Invalid input: message must be a non-empty string');
      return null;
    }

    const cleanMessage = message.trim();
    if (!cleanMessage) {
      console.error('[Transaction Parser] Empty message after trimming');
      return null;
    }

    // Step 2: Extract and validate transaction ID
    let transactionId = '';
    for (const pattern of TRANSACTION_ID_PATTERNS) {
      const match = cleanMessage.match(pattern);
      if (match) {
        transactionId = match[1];
        break;
      }
    }

    if (!transactionId) {
      console.error('[Transaction Parser] Failed to extract transaction ID from message');
      return null;
    }

    // Validate transaction ID format
    try {
      transactionId = validateTransactionId(transactionId);
      console.log(`[Transaction Parser] Transaction ID extracted: ${transactionId}`);
    } catch (error) {
      console.error(`[Transaction Parser] Invalid transaction ID format: ${transactionId}`, error);
      return null;
    }

    // Step 3: Extract and validate amount
    let amountStr = '';
    for (const pattern of AMOUNT_PATTERNS) {
      const match = cleanMessage.match(pattern);
      if (match) {
        amountStr = match[1].replace(/,/g, ''); // Remove commas
        break;
      }
    }

    if (!amountStr) {
      console.error('[Transaction Parser] Failed to extract amount from message');
      return null;
    }

    let amount: number;
    try {
      amount = validateAmount(amountStr);
      console.log(`[Transaction Parser] Amount extracted: KES ${amount}`);
    } catch (error) {
      console.error(`[Transaction Parser] Invalid amount: ${amountStr}`, error);
      return null;
    }

    // Step 4: Extract and validate customer name
    let customerNameStr = '';
    for (const pattern of CUSTOMER_NAME_PATTERNS) {
      const match = cleanMessage.match(pattern);
      if (match) {
        customerNameStr = match[1].trim();
        break;
      }
    }

    if (!customerNameStr) {
      console.error('[Transaction Parser] Failed to extract customer name from message');
      return null;
    }

    let customerName: string;
    try {
      customerName = validateCustomerName(customerNameStr);
      console.log(`[Transaction Parser] Customer name extracted: ${customerName}`);
    } catch (error) {
      console.error(`[Transaction Parser] Invalid customer name: ${customerNameStr}`, error);
      return null;
    }

    // Step 5: Extract and validate phone number
    let phoneNumberStr = '';
    for (const pattern of PHONE_NUMBER_PATTERNS) {
      const match = cleanMessage.match(pattern);
      if (match) {
        phoneNumberStr = match[1] || match[0];
        // Remove any non-digit characters
        phoneNumberStr = phoneNumberStr.replace(/\D/g, '');
        break;
      }
    }

    if (!phoneNumberStr) {
      console.error('[Transaction Parser] Failed to extract phone number from message');
      return null;
    }

    let phoneNumber: string;
    try {
      phoneNumber = validatePhoneNumber(phoneNumberStr);
      console.log(`[Transaction Parser] Phone number extracted: ${phoneNumber}`);
    } catch (error) {
      console.error(`[Transaction Parser] Invalid phone number: ${phoneNumberStr}`, error);
      return null;
    }

    // Extract transaction date using regex
    // Format: 14/05/2025 at 05:00 PM, 13/05/2025 at 12:55 PM, 15/04/2025 at 11:00 AM, etc.
    let dateMatch = cleanMessage.match(/(\d{1,2}\/\d{1,2}\/\d{4})\s+at\s+(\d{1,2}:\d{2}\s+[AP]M)/i);

    // Try alternative format if first pattern doesn't match
    if (!dateMatch) {
      dateMatch = cleanMessage.match(/on\s+(\d{1,2}\/\d{1,2}\/\d{4})\s+at\s+(\d{1,2}:\d{2}\s+[AP]M)/i);
    }

    // Try another format with different date separator
    if (!dateMatch) {
      dateMatch = cleanMessage.match(/(\d{1,2}-\d{1,2}-\d{4})\s+at\s+(\d{1,2}:\d{2}\s+[AP]M)/i);
    }

    let transactionDate: Date;

    if (dateMatch) {
      const dateStr = dateMatch[1]; // e.g., 14/05/2025 or 14-05-2025
      const timeStr = dateMatch[2]; // e.g., 05:00 PM

      // Parse date parts, handling both slash and dash separators
      const dateSeparator = dateStr.includes('/') ? '/' : '-';
      const [day, month, year] = dateStr.split(dateSeparator).map(Number);

      // Parse time parts
      const timeParts = timeStr.match(/(\d{1,2}):(\d{2})\s+([AP]M)/i);

      if (timeParts) {
        let hours = parseInt(timeParts[1]);
        const minutes = parseInt(timeParts[2]);
        const isPM = timeParts[3].toUpperCase() === 'PM';

        // Convert to 24-hour format
        if (isPM && hours < 12) {
          hours += 12;
        } else if (!isPM && hours === 12) {
          hours = 0;
        }

        // Create date object
        transactionDate = new Date(year, month - 1, day, hours, minutes);

        // Validate date (check if it's a valid date and not in the future)
        const now = new Date();
        if (isNaN(transactionDate.getTime()) || transactionDate > now) {
          console.warn('Invalid or future date detected, using current date instead');
          transactionDate = now;
        }
      } else {
        // Fallback to current date and time
        transactionDate = new Date();
      }
    } else {
      // Fallback to current date and time
      console.warn('No date found in message, using current date');
      transactionDate = new Date();
    }

    return {
      transactionId,
      amount,
      customerName,
      phoneNumber,
      transactionDate,
      rawMessage: cleanMessage
    };
  } catch (error) {
    console.error('Error parsing transaction message:', error);
    return null;
  }
}

/**
 * Batch parse multiple M-PESA transaction messages
 * @param messages Array of raw M-PESA transaction messages
 * @returns Array of successfully parsed transaction data
 */
export function parseTransactionMessages(messages: string[]): TransactionData[] {
  const results: TransactionData[] = [];

  for (const message of messages) {
    const parsedData = parseTransactionMessage(message);
    if (parsedData) {
      results.push(parsedData);
    }
  }

  return results;
}
