/**
 * Comprehensive test suite for the receipting system
 * Tests transaction parsing, receipt creation, validation, and PDF generation
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { parseTransactionMessage } from '../utils/transactionParser';
import { 
  validateTransactionId, 
  validatePhoneNumber, 
  validateAmount, 
  validateCustomerName,
  validateQuantity,
  validateServiceId,
  validateTransactionDate,
  validateReceiptData,
  validateReceiptItem,
  validateTransactionData
} from '../utils/validation';

// Mock Prisma client
jest.mock('../lib/prisma', () => ({
  __esModule: true,
  default: {
    transaction: {
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    receipt: {
      findFirst: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      findMany: jest.fn(),
    },
    service: {
      findUnique: jest.fn(),
    },
  },
}));

describe('Transaction Parser', () => {
  describe('parseTransactionMessage', () => {
    it('should parse valid M-Pesa transaction message', () => {
      const message = "TEE5PIQ2H7 completed. You have received KES 1500 from Wesley Moturi ************ for account MOCKY GRAPHICS LIMITED 7934479 on 14/05/2025 at 05:00 PM. KCB Go Ahead.";
      
      const result = parseTransactionMessage(message);
      
      expect(result).not.toBeNull();
      expect(result?.transactionId).toBe('TEE5PIQ2H7');
      expect(result?.amount).toBe(1500);
      expect(result?.customerName).toBe('Wesley Moturi');
      expect(result?.phoneNumber).toBe('************');
      expect(result?.rawMessage).toBe(message);
    });

    it('should parse alternative M-Pesa message format', () => {
      const message = "TED3JYV8SX Confirmed. Ksh2,500.00 received from JOHN DOE ************ on 15/05/2025 at 10:30 AM. New M-PESA balance is Ksh5,000.00";
      
      const result = parseTransactionMessage(message);
      
      expect(result).not.toBeNull();
      expect(result?.transactionId).toBe('TED3JYV8SX');
      expect(result?.amount).toBe(2500);
      expect(result?.customerName).toBe('JOHN DOE');
      expect(result?.phoneNumber).toBe('************');
    });

    it('should handle phone number format conversion', () => {
      const message = "TEF5PIQ2H7 Confirmed. Ksh1,500.00 received from JANE SMITH 0722000000 on 15/05/2025 at 10:30 AM.";
      
      const result = parseTransactionMessage(message);
      
      expect(result).not.toBeNull();
      expect(result?.phoneNumber).toBe('254722000000');
    });

    it('should return null for invalid message format', () => {
      const message = "Invalid message without proper format";
      
      const result = parseTransactionMessage(message);
      
      expect(result).toBeNull();
    });

    it('should return null for empty message', () => {
      const result = parseTransactionMessage('');
      expect(result).toBeNull();
    });

    it('should handle messages with missing transaction ID', () => {
      const message = "Confirmed. Ksh1,500.00 received from JOHN DOE ************";
      
      const result = parseTransactionMessage(message);
      
      expect(result).toBeNull();
    });

    it('should handle messages with invalid amount', () => {
      const message = "TEE5PIQ2H7 Confirmed. Invalid amount received from JOHN DOE ************";
      
      const result = parseTransactionMessage(message);
      
      expect(result).toBeNull();
    });
  });
});

describe('Validation Functions', () => {
  describe('validateTransactionId', () => {
    it('should validate correct M-Pesa transaction ID format', () => {
      expect(validateTransactionId('TEE5PIQ2H7')).toBe('TEE5PIQ2H7');
      expect(validateTransactionId('TED3JYV8SX')).toBe('TED3JYV8SX');
      expect(validateTransactionId('TDF9TBL1BV')).toBe('TDF9TBL1BV');
    });

    it('should throw error for invalid transaction ID format', () => {
      expect(() => validateTransactionId('INVALID')).toThrow('Invalid M-Pesa transaction ID format');
      expect(() => validateTransactionId('123456789')).toThrow('Invalid M-Pesa transaction ID format');
      expect(() => validateTransactionId('')).toThrow('Transaction ID is required');
    });
  });

  describe('validatePhoneNumber', () => {
    it('should validate and format Kenyan phone numbers', () => {
      expect(validatePhoneNumber('************')).toBe('************');
      expect(validatePhoneNumber('0720335304')).toBe('************');
      expect(validatePhoneNumber('720335304')).toBe('************');
      expect(validatePhoneNumber('+254 720 335 304')).toBe('************');
    });

    it('should throw error for invalid phone numbers', () => {
      expect(() => validatePhoneNumber('123')).toThrow('Invalid phone number format');
      expect(() => validatePhoneNumber('')).toThrow('Phone number is required');
      expect(() => validatePhoneNumber('999999999999')).toThrow('Invalid phone number format');
    });
  });

  describe('validateAmount', () => {
    it('should validate positive amounts', () => {
      expect(validateAmount(100)).toBe(100);
      expect(validateAmount('1500.50')).toBe(1500.5);
      expect(validateAmount(0.01)).toBe(0.01);
    });

    it('should round to 2 decimal places', () => {
      expect(validateAmount(100.999)).toBe(101);
      expect(validateAmount('1500.555')).toBe(1500.56);
    });

    it('should throw error for invalid amounts', () => {
      expect(() => validateAmount(0)).toThrow('Amount must be greater than zero');
      expect(() => validateAmount(-100)).toThrow('Amount must be greater than zero');
      expect(() => validateAmount('invalid')).toThrow('Amount must be a valid number');
      expect(() => validateAmount(20000000)).toThrow('Amount exceeds maximum limit');
    });
  });

  describe('validateCustomerName', () => {
    it('should validate proper customer names', () => {
      expect(validateCustomerName('John Doe')).toBe('John Doe');
      expect(validateCustomerName('Mary-Jane Smith')).toBe('Mary-Jane Smith');
      expect(validateCustomerName("O'Connor")).toBe("O'Connor");
      expect(validateCustomerName('Dr. Smith')).toBe('Dr. Smith');
    });

    it('should throw error for invalid names', () => {
      expect(() => validateCustomerName('')).toThrow('Customer name is required');
      expect(() => validateCustomerName('A')).toThrow('Customer name must be at least 2 characters long');
      expect(() => validateCustomerName('John123')).toThrow('Customer name contains invalid characters');
      expect(() => validateCustomerName('John@Doe')).toThrow('Customer name contains invalid characters');
    });
  });

  describe('validateQuantity', () => {
    it('should validate positive integers', () => {
      expect(validateQuantity(1)).toBe(1);
      expect(validateQuantity('5')).toBe(5);
      expect(validateQuantity(100)).toBe(100);
    });

    it('should throw error for invalid quantities', () => {
      expect(() => validateQuantity(0)).toThrow('Quantity must be greater than zero');
      expect(() => validateQuantity(-1)).toThrow('Quantity must be greater than zero');
      expect(() => validateQuantity(1.5)).toThrow('Quantity must be a valid integer');
      expect(() => validateQuantity('invalid')).toThrow('Quantity must be a valid integer');
      expect(() => validateQuantity(1001)).toThrow('Quantity cannot exceed 1000');
    });
  });

  describe('validateServiceId', () => {
    it('should validate UUID format', () => {
      const uuid = '123e4567-e89b-12d3-a456-************';
      expect(validateServiceId(uuid)).toBe(uuid);
      expect(validateServiceId(uuid.toUpperCase())).toBe(uuid);
    });

    it('should throw error for invalid UUID format', () => {
      expect(() => validateServiceId('invalid-uuid')).toThrow('Invalid service ID format');
      expect(() => validateServiceId('')).toThrow('Service ID is required');
      expect(() => validateServiceId('123')).toThrow('Invalid service ID format');
    });
  });
});

describe('Receipt Data Validation', () => {
  const validReceiptData = {
    transactionId: 'TEE5PIQ2H7',
    customerName: 'John Doe',
    phoneNumber: '************',
    email: '<EMAIL>',
    notes: 'Test receipt',
    items: [
      {
        serviceId: '123e4567-e89b-12d3-a456-************',
        quantity: 2,
        unitPrice: 1000,
        description: 'Logo design'
      }
    ]
  };

  describe('validateReceiptData', () => {
    it('should validate complete receipt data', () => {
      const result = validateReceiptData(validReceiptData);
      
      expect(result.transactionId).toBe('TEE5PIQ2H7');
      expect(result.customerName).toBe('John Doe');
      expect(result.phoneNumber).toBe('************');
      expect(result.email).toBe('<EMAIL>');
      expect(result.items).toHaveLength(1);
    });

    it('should handle optional fields', () => {
      const minimalData = {
        transactionId: 'TEE5PIQ2H7',
        items: [
          {
            serviceId: '123e4567-e89b-12d3-a456-************',
            quantity: 1
          }
        ]
      };
      
      const result = validateReceiptData(minimalData);
      
      expect(result.email).toBeNull();
      expect(result.notes).toBeNull();
      expect(result.customerName).toBe('');
      expect(result.phoneNumber).toBe('');
    });

    it('should throw error for missing required fields', () => {
      expect(() => validateReceiptData({})).toThrow('Transaction ID is required');
      expect(() => validateReceiptData({ transactionId: 'TEE5PIQ2H7' })).toThrow('At least one receipt item is required');
    });
  });

  describe('validateReceiptItem', () => {
    it('should validate receipt item with all fields', () => {
      const item = {
        serviceId: '123e4567-e89b-12d3-a456-************',
        quantity: 2,
        unitPrice: 1000,
        description: 'Logo design'
      };
      
      const result = validateReceiptItem(item);
      
      expect(result.serviceId).toBe('123e4567-e89b-12d3-a456-************');
      expect(result.quantity).toBe(2);
      expect(result.unitPrice).toBe(1000);
      expect(result.description).toBe('Logo design');
    });

    it('should handle optional unit price and description', () => {
      const item = {
        serviceId: '123e4567-e89b-12d3-a456-************',
        quantity: 1
      };
      
      const result = validateReceiptItem(item);
      
      expect(result.unitPrice).toBe(0);
      expect(result.description).toBeNull();
    });
  });
});
