/**
 * Service for managing receipts
 * Enhanced with comprehensive validation, error handling, and security measures
 */
import prisma from '@/lib/prisma';
import { generateReceiptPDF, ReceiptData, ReceiptItemData } from '@/utils/pdfGenerator';
import { getTransactionById } from './transactionService';
import { getServiceById } from './serviceItemService';
import {
  validateReceiptData,
  validateTransactionId,
  ValidatedReceiptData,
  TeamMemberValidationError
} from '@/utils/validation';
import path from 'path';
import { promises as fs } from 'fs';

export interface Receipt {
  id: string;
  receiptNumber: string;
  totalAmount: number;
  amountPaid: number;
  balance: number;
  customerName: string;
  phoneNumber: string;
  email: string | null;
  status: string;
  notes: string | null;
  createdAt: Date;
  updatedAt: Date;
  issuedAt: Date;
  paidAt: Date | null;
  transactionId: string;
  items: ReceiptItem[];
  pdfUrl?: string;
}

export interface ReceiptItem {
  id: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  description: string | null;
  createdAt: Date;
  updatedAt: Date;
  receiptId: string;
  serviceId: string;
  serviceName?: string;
}

/**
 * Generate a unique receipt number
 * @param mpesaTransactionId Optional M-Pesa transaction ID to use as the receipt number
 * @returns A unique receipt number
 */
async function generateReceiptNumber(mpesaTransactionId?: string): Promise<string> {
  // If M-Pesa transaction ID is provided, use it as the receipt number
  if (mpesaTransactionId && mpesaTransactionId.trim() !== '') {
    console.log(`Using M-Pesa transaction ID as receipt number: ${mpesaTransactionId}`);

    // Check if a receipt with this number already exists
    const existingReceipt = await prisma.receipt.findUnique({
      where: {
        receiptNumber: mpesaTransactionId
      }
    });

    if (existingReceipt) {
      console.log(`Receipt with number ${mpesaTransactionId} already exists, generating alternative number`);
      // If it exists, append a suffix to make it unique
      const timestamp = Date.now().toString().slice(-4);
      return `${mpesaTransactionId}-${timestamp}`;
    }

    return mpesaTransactionId;
  }

  // Otherwise, generate a receipt number in the traditional format
  const date = new Date();
  const year = date.getFullYear().toString().slice(-2);
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');

  // Get the count of receipts for today
  const todayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  const todayEnd = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1);

  const count = await prisma.receipt.count({
    where: {
      createdAt: {
        gte: todayStart,
        lt: todayEnd
      }
    }
  });

  // Generate receipt number in format: MG-YYMMDD-XXX
  const sequence = (count + 1).toString().padStart(3, '0');
  const receiptNumber = `MG-${year}${month}${day}-${sequence}`;

  // Ensure the generated number is unique
  const existingReceipt = await prisma.receipt.findUnique({
    where: {
      receiptNumber
    }
  });

  if (existingReceipt) {
    // If it exists, append a timestamp to make it unique
    const timestamp = Date.now().toString().slice(-4);
    return `${receiptNumber}-${timestamp}`;
  }

  return receiptNumber;
}

/**
 * Create a new receipt with comprehensive validation and error handling
 * @param data The receipt data
 * @returns The created receipt
 */
export async function createReceipt(data: {
  transactionId: string;
  customerName?: string;
  phoneNumber?: string;
  email?: string;
  notes?: string;
  items: {
    serviceId: string;
    quantity: number;
    unitPrice?: number;
    description?: string;
  }[];
}): Promise<Receipt | null> {
  const startTime = Date.now();

  try {
    console.log(`[Receipt Service] Starting receipt creation for transaction: ${data.transactionId}`);

    // Step 1: Validate input data
    let validatedData: ValidatedReceiptData;
    try {
      validatedData = validateReceiptData(data);
      console.log(`[Receipt Service] Input validation successful`);
    } catch (validationError) {
      console.error(`[Receipt Service] Validation failed:`, validationError);
      throw new Error(`Validation failed: ${validationError instanceof Error ? validationError.message : 'Invalid data'}`);
    }

    // Step 2: Get and validate the transaction
    const transaction = await getTransactionById(validatedData.transactionId);
    if (!transaction) {
      console.error(`[Receipt Service] Transaction not found: ${validatedData.transactionId}`);
      throw new Error(`Transaction ${validatedData.transactionId} not found`);
    }

    console.log(`[Receipt Service] Transaction found: ${transaction.transactionId} - ${transaction.customerName} - KES ${transaction.amount}`);

    // Step 3: Check for existing receipt (prevent duplicates)
    const existingReceipt = await prisma.receipt.findFirst({
      where: {
        transactionId: validatedData.transactionId
      },
      include: {
        items: true
      }
    });

    if (existingReceipt) {
      console.log(`[Receipt Service] Receipt already exists for transaction ${validatedData.transactionId}: ${existingReceipt.receiptNumber}`);
      // Return the existing receipt with proper formatting
      return formatReceipt(existingReceipt);
    }

    // Step 4: Generate unique receipt number
    const receiptNumber = await generateReceiptNumber(transaction.transactionId);
    console.log(`[Receipt Service] Generated receipt number: ${receiptNumber}`);

    // Step 5: Process and validate receipt items with database transaction
    const receiptItems = [];
    let totalAmount = 0;

    console.log(`[Receipt Service] Processing ${validatedData.items.length} receipt items`);

    for (let i = 0; i < validatedData.items.length; i++) {
      const item = validatedData.items[i];

      try {
        // Get and validate service details
        const service = await getServiceById(item.serviceId);
        if (!service) {
          throw new Error(`Service ${item.serviceId} not found`);
        }

        // Use provided unit price or service price (with validation)
        const unitPrice = item.unitPrice > 0 ? item.unitPrice : Number(service.price);
        if (unitPrice <= 0) {
          throw new Error(`Invalid unit price for service ${service.name}`);
        }

        const totalPrice = Math.round((unitPrice * item.quantity) * 100) / 100; // Round to 2 decimal places

        receiptItems.push({
          serviceId: item.serviceId,
          quantity: item.quantity,
          unitPrice,
          totalPrice,
          description: item.description || service.name
        });

        totalAmount += totalPrice;
        console.log(`[Receipt Service] Item ${i + 1}: ${service.name} x${item.quantity} @ KES ${unitPrice} = KES ${totalPrice}`);

      } catch (itemError) {
        console.error(`[Receipt Service] Error processing item ${i + 1}:`, itemError);
        throw new Error(`Error processing item ${i + 1}: ${itemError instanceof Error ? itemError.message : 'Unknown error'}`);
      }
    }

    // Round total amount to 2 decimal places
    totalAmount = Math.round(totalAmount * 100) / 100;
    console.log(`[Receipt Service] Total amount calculated: KES ${totalAmount}`);

    // Step 6: Calculate balance and determine status
    const amountPaid = Number(transaction.amount);
    const balance = Math.round((totalAmount - amountPaid) * 100) / 100;
    const status = balance <= 0 ? 'paid' : 'issued';

    console.log(`[Receipt Service] Amount paid: KES ${amountPaid}, Balance: KES ${balance}, Status: ${status}`);

    // Step 7: Create receipt and update transaction in a database transaction
    let receipt;
    try {
      receipt = await prisma.$transaction(async (tx) => {
        // Create the receipt
        const newReceipt = await tx.receipt.create({
          data: {
            receiptNumber,
            totalAmount,
            amountPaid,
            balance,
            customerName: validatedData.customerName || transaction.customerName,
            phoneNumber: validatedData.phoneNumber || transaction.phoneNumber,
            email: validatedData.email,
            status,
            notes: validatedData.notes,
            issuedAt: new Date(),
            paidAt: status === 'paid' ? new Date() : null,
            transactionId: transaction.id,
            items: {
              create: receiptItems
            }
          },
          include: {
            items: true
          }
        });

        // Update transaction status
        await tx.transaction.update({
          where: {
            id: transaction.id
          },
          data: {
            status: 'processed',
            processedAt: new Date()
          }
        });

        return newReceipt;
      });

      console.log(`[Receipt Service] Receipt created successfully: ${receipt.receiptNumber}`);
    } catch (dbError) {
      console.error(`[Receipt Service] Database transaction failed:`, dbError);
      throw new Error(`Failed to create receipt: ${dbError instanceof Error ? dbError.message : 'Database error'}`);
    }

    // Step 8: Format the receipt
    const formattedReceipt = formatReceipt(receipt);

    // Step 9: Generate PDF file with enhanced error handling
    let pdfUrl: string;
    try {
      console.log(`[Receipt Service] Generating PDF for receipt: ${formattedReceipt.receiptNumber}`);

      // Convert receipt to receipt data for PDF generation
      const receiptData = mapToReceiptData(formattedReceipt);

      // Generate the PDF file
      pdfUrl = await generateReceiptPDF(receiptData);

      console.log(`[Receipt Service] PDF generated successfully: ${pdfUrl}`);

      // Update the receipt with the PDF URL
      await prisma.receipt.update({
        where: { id: receipt.id },
        data: { pdfUrl }
      });

    } catch (pdfError) {
      console.error(`[Receipt Service] PDF generation failed:`, pdfError);

      // Generate a consistent PDF filename based on receipt number and ID as fallback
      const pdfFilename = `receipt-${formattedReceipt.receiptNumber}-${formattedReceipt.id.substring(0, 8)}.pdf`;
      pdfUrl = `/uploads/receipts/${pdfFilename}`;

      console.log(`[Receipt Service] Using fallback PDF URL: ${pdfUrl}`);

      // Update the receipt with the fallback PDF URL
      try {
        await prisma.receipt.update({
          where: { id: receipt.id },
          data: { pdfUrl }
        });
      } catch (updateError) {
        console.error(`[Receipt Service] Failed to update receipt with PDF URL:`, updateError);
        // Continue anyway - receipt was created successfully
      }
    }

    // Step 10: Log completion and return result
    const duration = Date.now() - startTime;
    console.log(`[Receipt Service] Receipt creation completed in ${duration}ms: ${formattedReceipt.receiptNumber}`);

    return {
      ...formattedReceipt,
      pdfUrl
    };

  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[Receipt Service] Receipt creation failed after ${duration}ms:`, error);

    // Return more specific error information
    if (error instanceof TeamMemberValidationError) {
      throw error; // Re-throw validation errors as-is
    }

    throw new Error(`Receipt creation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Get all receipts
 * @returns Array of all receipts
 */
export async function getAllReceipts(): Promise<Receipt[]> {
  try {
    const receipts = await prisma.receipt.findMany({
      include: {
        items: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Format receipts and add PDF URLs
    const formattedReceipts = receipts.map(receipt => {
      const formatted = formatReceipt(receipt);

      // Use existing PDF URL if available
      if (receipt.pdfUrl) {
        formatted.pdfUrl = receipt.pdfUrl;
      } else {
        // Generate a consistent PDF filename based on receipt number and ID
        const pdfFilename = `receipt-${formatted.receiptNumber}-${formatted.id.substring(0, 8)}.pdf`;
        const pdfUrl = `/uploads/receipts/${pdfFilename}`;
        formatted.pdfUrl = pdfUrl;
      }

      return formatted;
    });

    return formattedReceipts;
  } catch (error) {
    console.error('Error getting all receipts:', error);
    throw error;
  }
}

/**
 * Get a receipt by ID
 * @param id The receipt ID
 * @returns The receipt or null if not found
 */
export async function getReceiptById(id: string): Promise<Receipt | null> {
  try {
    const receipt = await prisma.receipt.findUnique({
      where: {
        id
      },
      include: {
        items: true
      }
    });

    if (!receipt) {
      return null;
    }

    // Format the receipt
    const formattedReceipt = formatReceipt(receipt);

    // Check if the receipt already has a PDF URL
    if (receipt.pdfUrl) {
      formattedReceipt.pdfUrl = receipt.pdfUrl;

      // Check if the PDF file exists
      const pdfPath = path.join(process.cwd(), 'public', receipt.pdfUrl);
      try {
        await fs.access(pdfPath);
        // PDF exists, return the receipt
        return formattedReceipt;
      } catch (fileError) {
        // PDF doesn't exist, we'll generate it below
        console.log(`PDF file not found at ${pdfPath}, will generate a new one`);
      }
    }

    // Generate PDF file
    try {
      // Convert receipt to receipt data for PDF generation
      const receiptData = mapToReceiptData(formattedReceipt);

      // Generate the PDF file
      const pdfUrl = await generateReceiptPDF(receiptData);

      // Update the receipt with the PDF URL
      await prisma.receipt.update({
        where: { id: receipt.id },
        data: { pdfUrl }
      });

      // Set the PDF URL
      formattedReceipt.pdfUrl = pdfUrl;
    } catch (pdfError) {
      console.error('Error generating PDF for receipt:', pdfError);

      // Generate a consistent PDF filename based on receipt number and ID as fallback
      const pdfFilename = `receipt-${formattedReceipt.receiptNumber}-${formattedReceipt.id.substring(0, 8)}.pdf`;
      const pdfUrl = `/uploads/receipts/${pdfFilename}`;

      // Set the PDF URL
      formattedReceipt.pdfUrl = pdfUrl;
    }

    return formattedReceipt;
  } catch (error) {
    console.error(`Error getting receipt ${id}:`, error);
    throw error;
  }
}

/**
 * Check if a receipt exists for a given M-Pesa transaction ID
 * @param mpesaTransactionId The M-Pesa transaction ID
 * @returns The receipt ID and receipt number if found, null otherwise
 */
export async function checkReceiptByMpesaTransactionId(mpesaTransactionId: string): Promise<{ id: string; receiptNumber: string } | null> {
  try {
    // First, find a receipt with this M-Pesa transaction ID as the receipt number
    // (since we use the M-Pesa transaction ID as the receipt number)
    const receipt = await prisma.receipt.findFirst({
      where: {
        receiptNumber: {
          startsWith: mpesaTransactionId
        }
      },
      select: {
        id: true,
        receiptNumber: true
      }
    });

    if (receipt) {
      return {
        id: receipt.id,
        receiptNumber: receipt.receiptNumber
      };
    }

    // If not found directly, try to find through the transaction
    // First find the transaction with this M-Pesa transaction ID
    const transaction = await prisma.transaction.findUnique({
      where: {
        transactionId: mpesaTransactionId
      },
      select: {
        id: true
      }
    });

    if (!transaction) {
      return null;
    }

    // Then check if there's a receipt for this transaction
    const receiptByTransaction = await prisma.receipt.findFirst({
      where: {
        transactionId: transaction.id
      },
      select: {
        id: true,
        receiptNumber: true
      }
    });

    if (receiptByTransaction) {
      return {
        id: receiptByTransaction.id,
        receiptNumber: receiptByTransaction.receiptNumber
      };
    }

    return null;
  } catch (error) {
    console.error(`Error checking receipt for M-Pesa transaction ID ${mpesaTransactionId}:`, error);
    return null;
  }
}

/**
 * Get a receipt by receipt number
 * @param receiptNumber The receipt number
 * @returns The receipt or null if not found
 */
export async function getReceiptByNumber(receiptNumber: string): Promise<Receipt | null> {
  try {
    const receipt = await prisma.receipt.findUnique({
      where: {
        receiptNumber
      },
      include: {
        items: true
      }
    });

    if (!receipt) {
      return null;
    }

    // Format the receipt
    const formattedReceipt = formatReceipt(receipt);

    // Check if the receipt already has a PDF URL
    if (receipt.pdfUrl) {
      formattedReceipt.pdfUrl = receipt.pdfUrl;

      // Check if the PDF file exists
      const pdfPath = path.join(process.cwd(), 'public', receipt.pdfUrl);
      try {
        await fs.access(pdfPath);
        // PDF exists, return the receipt
        return formattedReceipt;
      } catch (fileError) {
        // PDF doesn't exist, we'll generate it below
        console.log(`PDF file not found at ${pdfPath}, will generate a new one`);
      }
    }

    // Generate PDF file
    try {
      // Convert receipt to receipt data for PDF generation
      const receiptData = mapToReceiptData(formattedReceipt);

      // Generate the PDF file
      const pdfUrl = await generateReceiptPDF(receiptData);

      // Update the receipt with the PDF URL
      await prisma.receipt.update({
        where: { id: receipt.id },
        data: { pdfUrl }
      });

      // Set the PDF URL
      formattedReceipt.pdfUrl = pdfUrl;
    } catch (pdfError) {
      console.error('Error generating PDF for receipt:', pdfError);

      // Generate a consistent PDF filename based on receipt number and ID as fallback
      const pdfFilename = `receipt-${formattedReceipt.receiptNumber}-${formattedReceipt.id.substring(0, 8)}.pdf`;
      const pdfUrl = `/uploads/receipts/${pdfFilename}`;

      // Set the PDF URL
      formattedReceipt.pdfUrl = pdfUrl;
    }

    return formattedReceipt;
  } catch (error) {
    console.error(`Error getting receipt ${receiptNumber}:`, error);
    throw error;
  }
}

/**
 * Format a receipt from the database
 * @param receipt The receipt from the database
 * @returns The formatted receipt
 */
function formatReceipt(receipt: any): Receipt {
  return {
    id: receipt.id,
    receiptNumber: receipt.receiptNumber,
    totalAmount: Number(receipt.totalAmount),
    amountPaid: Number(receipt.amountPaid),
    balance: Number(receipt.balance),
    customerName: receipt.customerName,
    phoneNumber: receipt.phoneNumber,
    email: receipt.email,
    status: receipt.status,
    notes: receipt.notes,
    createdAt: new Date(receipt.createdAt),
    updatedAt: new Date(receipt.updatedAt),
    issuedAt: new Date(receipt.issuedAt),
    paidAt: receipt.paidAt ? new Date(receipt.paidAt) : null,
    transactionId: receipt.transactionId,
    pdfUrl: receipt.pdfUrl || undefined,
    items: receipt.items.map((item: any) => ({
      id: item.id,
      quantity: item.quantity,
      unitPrice: Number(item.unitPrice),
      totalPrice: Number(item.totalPrice),
      description: item.description,
      createdAt: new Date(item.createdAt),
      updatedAt: new Date(item.updatedAt),
      receiptId: item.receiptId,
      serviceId: item.serviceId
    }))
  };
}

/**
 * Map a receipt to receipt data for PDF generation
 * @param receipt The receipt
 * @returns The receipt data for PDF generation
 */
export function mapToReceiptData(receipt: Receipt): ReceiptData {
  return {
    receiptNumber: receipt.receiptNumber,
    customerName: receipt.customerName,
    phoneNumber: receipt.phoneNumber,
    email: receipt.email || undefined,
    transactionId: receipt.transactionId,
    transactionDate: receipt.issuedAt,
    items: receipt.items.map((item): ReceiptItemData => ({
      description: item.description || 'Service',
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      totalPrice: item.totalPrice
    })),
    totalAmount: receipt.totalAmount,
    amountPaid: receipt.amountPaid,
    balance: receipt.balance,
    notes: receipt.notes || undefined,
    issuedAt: receipt.issuedAt
  };
}
